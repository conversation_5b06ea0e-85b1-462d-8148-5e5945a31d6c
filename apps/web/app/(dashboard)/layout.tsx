"use client";

import { useState, useEffect } from "react";
import { useAtomValue } from "jotai";
import { firebaseUserAtom } from "@/app/store/authAtom"; // Correct path to your atom
import { signOut } from "firebase/auth";
import { auth } from "@/lib/firebase";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import {
  SidebarProvider,
  SidebarTrigger,
  SidebarInset, // <-- IMPORT THIS
} from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/AppSidebar";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Plus, User, Settings, LogOut, Search } from "lucide-react";
import { CommandBar } from "@/components/CommandBar";
import { QuickAddModal } from "@/components/modals/QuickAddModal";

export default function AppLayout({ children }: { children: React.ReactNode }) {
  const [showQuickAddModal, setShowQuickAddModal] = useState(false);
  const [commandBarOpen, setCommandBarOpen] = useState(false);
  const user = useAtomValue(firebaseUserAtom);
  const router = useRouter();

  // useEffect and handleSignOut functions remain the same...
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setCommandBarOpen(true);
      }
    };
    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, []);

  const handleSignOut = async () => {
    try {
      await signOut(auth);
      toast.success("Signed out successfully");
      router.push("/login");
    } catch (error) {
      toast.error("Failed to sign out");
    }
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />

        {/* FIX: Wrap your header and main content in the SidebarInset component */}
        <SidebarInset className="flex flex-col flex-1">
          {/* Header Bar */}
          <header className="h-16 border-b border-border bg-card/50 backdrop-blur-sm sticky top-0 z-40">
            <div className="flex items-center justify-between h-full px-6">
              <div className="flex items-center space-x-4">
                <SidebarTrigger className="hover:bg-accent" />
                <Button
                  variant="outline"
                  onClick={() => setCommandBarOpen(true)}
                  className="w-96 max-w-md justify-start text-muted-foreground"
                >
                  <Search className="h-4 w-4 mr-2" />
                  Search...
                  <div className="ml-auto">
                    <kbd className="pointer-events-none h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium">
                      ⌘K
                    </kbd>
                  </div>
                </Button>
              </div>

              <div className="flex items-center space-x-4">
                <Button onClick={() => setShowQuickAddModal(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Quick Add
                </Button>

                {/* User Profile Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className="relative h-10 w-10 rounded-full"
                    >
                      <Avatar className="h-10 w-10">
                        <AvatarImage
                          src={user?.photoURL || ""}
                          alt={user?.displayName || "User"}
                        />
                        <AvatarFallback>
                          {user?.displayName?.charAt(0) ||
                            user?.email?.charAt(0) ||
                            "U"}
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56" align="end" forceMount>
                    <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">
                          {user?.displayName}
                        </p>
                        <p className="text-xs leading-none text-muted-foreground">
                          {user?.email}
                        </p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onSelect={() => router.push("/settings")}>
                      <Settings className="mr-2 h-4 w-4" />
                      <span>Settings</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onSelect={handleSignOut}
                      className="text-destructive"
                    >
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Logout</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </header>

          {/* Main Content Area */}
          <main className="flex-1 overflow-auto p-6">{children}</main>
        </SidebarInset>
      </div>

      <CommandBar
        isOpen={commandBarOpen}
        onClose={() => setCommandBarOpen(false)}
      />

      <QuickAddModal
        open={showQuickAddModal}
        onOpenChange={setShowQuickAddModal}
      />
    </SidebarProvider>
  );
}
