"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Home,
  Library,
  Sparkles,
  Tag,
  Link2,
  Settings,
  LogOut,
  Inbox,
  Folder,
  ChevronRight,
  ChevronDown,
  Plus,
} from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  useSidebar,
} from "@/components/ui/sidebar";
import { Logo } from "@/components/Logo";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { useState } from "react";
import { AddFolderModal } from "@/components/modals/AddFolderModal";

const navigationItems = [
  { title: "Dashboard", href: "/dashboard", icon: Home },
  { title: "Inbox", href: "/inbox", icon: Inbox },
  { title: "All Content", href: "/content", icon: Library },
  { title: "My Gists", href: "/gists", icon: Sparkles },
];

const mockFolders = [
  {
    id: 1,
    name: "Development",
    count: 12,
    children: [
      { id: 2, name: "React", count: 8, href: "/folders/react" },
      { id: 3, name: "TypeScript", count: 4, href: "/folders/typescript" },
    ],
    href: "/folders/development",
  },
  {
    id: 4,
    name: "Design",
    count: 6,
    children: [
      { id: 5, name: "UI/UX", count: 4, href: "/folders/ui-ux" },
      { id: 6, name: "Icons", count: 2, href: "/folders/icons" },
    ],
    href: "/folders/design",
  },
  { id: 7, name: "Marketing", count: 3, href: "/folders/marketing" },
];

export function AppSidebar() {
  const { open } = useSidebar();
  const currentPath = usePathname();
  const [foldersOpen, setFoldersOpen] = useState(true);
  const [expandedFolders, setExpandedFolders] = useState<Set<number>>(
    new Set([1, 4])
  );
  const [showAddFolderModal, setShowAddFolderModal] = useState(false);

  const isActive = (path: string) => currentPath === path;

  const toggleFolder = (folderId: number) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(folderId)) {
      newExpanded.delete(folderId);
    } else {
      newExpanded.add(folderId);
    }
    setExpandedFolders(newExpanded);
  };

  const handleLogout = () => {
    toast.success("Signed out successfully");
    // Add Firebase sign-out logic here
  };

  return (
    <>
      <Sidebar variant="sidebar" collapsible="icon">
        <SidebarHeader className="border-b border-border p-4">
          <div className="flex items-center justify-center">
            <Logo
              variant={open ? "full" : "icon"}
              size={open ? "md" : "sm"}
              className="transition-all duration-300"
            />
          </div>
        </SidebarHeader>

        <SidebarContent>
          {/* Main Navigation */}
          <SidebarGroup>
            <SidebarGroupContent>
              <SidebarMenu className="space-y-1">
                {navigationItems.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      asChild
                      className="transition-all duration-200"
                    >
                      <Link
                        href={item.href}
                        className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 ${
                          isActive(item.href)
                            ? "bg-primary text-primary-foreground shadow-glow"
                            : "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                        }`}
                      >
                        <item.icon className="h-5 w-5 flex-shrink-0" />
                        {open && (
                          <span className="font-medium truncate">
                            {item.title}
                          </span>
                        )}
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>

          {/* Folders Section */}
          <Collapsible open={foldersOpen} onOpenChange={setFoldersOpen}>
            <SidebarGroup>
              <CollapsibleTrigger asChild>
                <SidebarGroupLabel className="flex items-center justify-between cursor-pointer hover:bg-sidebar-accent rounded px-2 py-1">
                  <span className="flex items-center gap-2">
                    <Folder className="h-4 w-4" />
                    {open && "Folders"}
                  </span>
                  <div className="flex items-center gap-1">
                    {open && (
                      <div
                        className="h-5 w-5 p-0 hover:bg-sidebar-accent rounded flex items-center justify-center cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowAddFolderModal(true);
                        }}
                      >
                        <Plus className="h-3 w-3" />
                      </div>
                    )}
                    {open &&
                      (foldersOpen ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      ))}
                  </div>
                </SidebarGroupLabel>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <SidebarGroupContent>
                  <SidebarMenu className="space-y-1">
                    {mockFolders.map((folder) => (
                      <div key={folder.id}>
                        <SidebarMenuItem>
                          <SidebarMenuButton className="flex items-center justify-between w-full">
                            <div className="flex items-center space-x-2">
                              {folder.children && (
                                <div
                                  onClick={() => toggleFolder(folder.id)}
                                  className="p-0 h-4 w-4 flex items-center justify-center cursor-pointer"
                                >
                                  {expandedFolders.has(folder.id) ? (
                                    <ChevronDown className="h-3 w-3" />
                                  ) : (
                                    <ChevronRight className="h-3 w-3" />
                                  )}
                                </div>
                              )}
                              <Folder className="h-4 w-4 flex-shrink-0" />
                              {open && (
                                <span className="truncate">{folder.name}</span>
                              )}
                            </div>
                            {open && (
                              <span className="text-xs text-muted-foreground bg-sidebar-accent px-1.5 py-0.5 rounded">
                                {folder.count}
                              </span>
                            )}
                          </SidebarMenuButton>
                        </SidebarMenuItem>

                        {/* Folder Children */}
                        {folder.children && expandedFolders.has(folder.id) && (
                          <div className="ml-4 space-y-1">
                            {folder.children.map((child) => (
                              <SidebarMenuItem key={child.id}>
                                <SidebarMenuButton className="flex items-center justify-between w-full">
                                  <div className="flex items-center space-x-2">
                                    <div className="w-4" />{" "}
                                    {/* Spacing for alignment */}
                                    <Folder className="h-3 w-3 flex-shrink-0 opacity-60" />
                                    {open && (
                                      <span className="truncate text-sm">
                                        {child.name}
                                      </span>
                                    )}
                                  </div>
                                  {open && (
                                    <span className="text-xs text-muted-foreground bg-sidebar-accent px-1.5 py-0.5 rounded">
                                      {child.count}
                                    </span>
                                  )}
                                </SidebarMenuButton>
                              </SidebarMenuItem>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </SidebarMenu>
                </SidebarGroupContent>
              </CollapsibleContent>
            </SidebarGroup>
          </Collapsible>

          {/* Tags Section */}
          <SidebarGroup>
            <SidebarGroupContent>
              <SidebarMenu>
                <SidebarMenuItem>
                  <SidebarMenuButton asChild>
                    <Link
                      href="/tags"
                      className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 ${
                        isActive("/tags")
                          ? "bg-primary text-primary-foreground"
                          : "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                      }`}
                    >
                      <Tag className="h-5 w-5 flex-shrink-0" />
                      {open && <span className="font-medium">Tags</span>}
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuButton asChild>
                    <Link
                      href="/shared"
                      className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 ${
                        isActive("/shared")
                          ? "bg-primary text-primary-foreground"
                          : "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                      }`}
                    >
                      <Link2 className="h-5 w-5 flex-shrink-0" />
                      {open && (
                        <span className="font-medium">Shared Links</span>
                      )}
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>

        <SidebarFooter className="border-t border-border p-4">
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <Link
                  href="/settings"
                  className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 ${
                    isActive("/settings")
                      ? "bg-primary text-primary-foreground"
                      : "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                  }`}
                >
                  <Settings className="h-5 w-5 flex-shrink-0" />
                  {open && <span className="font-medium">Settings</span>}
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <SidebarMenuButton asChild>
                <Button
                  variant="ghost"
                  onClick={handleLogout}
                  className="w-full justify-start space-x-3 px-3 py-2 h-auto hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                >
                  <LogOut className="h-5 w-5 flex-shrink-0" />
                  {open && <span className="font-medium">Logout</span>}
                </Button>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
      </Sidebar>

      <AddFolderModal
        open={showAddFolderModal}
        onOpenChange={setShowAddFolderModal}
      />
    </>
  );
}
