"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Home,
  Library,
  Sparkles,
  Tag,
  Link2,
  Settings,
  LogOut,
  Inbox,
  Folder,
  ChevronRight,
  ChevronDown,
  Plus,
} from "lucide-react";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar";
import { Logo } from "@/components/Logo";
import { toast } from "sonner";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { useState } from "react";
import { AddFolderModal } from "@/components/modals/AddFolderModal";

const navigationItems = [
  { title: "Dashboard", href: "/dashboard", icon: Home },
  { title: "Inbox", href: "/inbox", icon: Inbox },
  { title: "All Content", href: "/content", icon: Library },
  { title: "My Gists", href: "/gists", icon: Sparkles },
];

const mockFolders = [
  {
    id: 1,
    name: "<PERSON>",
    count: 12,
    children: [
      { id: 2, name: "React", count: 8, href: "/folders/react" },
      { id: 3, name: "TypeScript", count: 4, href: "/folders/typescript" },
    ],
    href: "/folders/development",
  },
  {
    id: 4,
    name: "Design",
    count: 6,
    children: [
      { id: 5, name: "UI/UX", count: 4, href: "/folders/ui-ux" },
      { id: 6, name: "Icons", count: 2, href: "/folders/icons" },
    ],
    href: "/folders/design",
  },
  { id: 7, name: "Marketing", count: 3, href: "/folders/marketing" },
];

export function AppSidebar() {
  const currentPath = usePathname();
  const [foldersOpen, setFoldersOpen] = useState(true);
  const [expandedFolders, setExpandedFolders] = useState<Set<number>>(
    new Set([1, 4])
  );
  const [showAddFolderModal, setShowAddFolderModal] = useState(false);

  const isActive = (path: string) => currentPath === path;

  const toggleFolder = (folderId: number) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(folderId)) {
      newExpanded.delete(folderId);
    } else {
      newExpanded.add(folderId);
    }
    setExpandedFolders(newExpanded);
  };

  const handleLogout = () => {
    toast.success("Signed out successfully");
    // Add Firebase sign-out logic here
  };

  return (
    <>
      <Sidebar
        variant="sidebar"
        collapsible="icon"
        className="h-full flex flex-col"
      >
        <SidebarHeader className="border-b border-sidebar-border h-16 flex items-center px-4">
          <div className="group-data-[collapsible=icon]:hidden">
            <Logo variant="full" size="md" />
          </div>
          <div className="hidden group-data-[collapsible=icon]:block">
            <Logo variant="icon" size="sm" />
          </div>
        </SidebarHeader>

        <SidebarContent className="flex-1 overflow-y-auto px-2 py-4">
          {/* Main Navigation */}
          <SidebarGroup>
            <SidebarMenu className="space-y-1">
              {navigationItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild isActive={isActive(item.href)}>
                    <Link href={item.href}>
                      <item.icon className="h-5 w-5" />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroup>

          {/* Folders Section */}
          <Collapsible open={foldersOpen} onOpenChange={setFoldersOpen}>
            <SidebarGroup>
              <SidebarGroupLabel asChild>
                <CollapsibleTrigger className="flex items-center justify-between cursor-pointer hover:bg-sidebar-accent rounded-md px-2 py-1 mx-2">
                  <span className="flex items-center gap-2">
                    <Folder className="h-4 w-4" />
                    <span>Folders</span>
                  </span>
                  <div className="flex items-center gap-1">
                    <div
                      className="h-5 w-5 p-0 hover:bg-sidebar-accent rounded flex items-center justify-center cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        setShowAddFolderModal(true);
                      }}
                    >
                      <Plus className="h-3 w-3" />
                    </div>
                    {foldersOpen ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </div>
                </CollapsibleTrigger>
              </SidebarGroupLabel>
              <CollapsibleContent>
                <SidebarMenu className="space-y-1 mt-1">
                  {mockFolders.map((folder) => (
                    <div key={folder.id}>
                      <SidebarMenuItem>
                        <SidebarMenuButton
                          asChild
                          isActive={isActive(folder.href)}
                        >
                          <Link
                            href={folder.href}
                            className="flex items-center justify-between w-full"
                          >
                            <div className="flex items-center space-x-2">
                              {folder.children && (
                                <div
                                  onClick={(e) => {
                                    e.preventDefault();
                                    toggleFolder(folder.id);
                                  }}
                                  className="p-0 h-4 w-4 flex items-center justify-center cursor-pointer"
                                >
                                  {expandedFolders.has(folder.id) ? (
                                    <ChevronDown className="h-3 w-3" />
                                  ) : (
                                    <ChevronRight className="h-3 w-3" />
                                  )}
                                </div>
                              )}
                              <Folder className="h-4 w-4" />
                              <span className="truncate">{folder.name}</span>
                            </div>
                            <span className="text-xs text-muted-foreground bg-sidebar-accent px-1.5 py-0.5 rounded">
                              {folder.count}
                            </span>
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuItem>

                      {/* Folder Children */}
                      {folder.children && expandedFolders.has(folder.id) && (
                        <div className="ml-5 space-y-1 mt-1">
                          {folder.children.map((child) => (
                            <SidebarMenuItem key={child.id}>
                              <SidebarMenuButton
                                asChild
                                isActive={isActive(child.href)}
                                size="sm"
                              >
                                <Link
                                  href={child.href}
                                  className="flex items-center justify-between w-full pl-6"
                                >
                                  <div className="flex items-center space-x-2">
                                    <Folder className="h-3 w-3 opacity-60" />
                                    <span className="truncate text-sm">
                                      {child.name}
                                    </span>
                                  </div>
                                  <span className="text-xs text-muted-foreground bg-sidebar-accent px-1.5 py-0.5 rounded">
                                    {child.count}
                                  </span>
                                </Link>
                              </SidebarMenuButton>
                            </SidebarMenuItem>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </SidebarMenu>
              </CollapsibleContent>
            </SidebarGroup>
          </Collapsible>

          {/* Tags & Shared Links Section */}
          <SidebarGroup>
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton asChild isActive={isActive("/tags")}>
                  <Link href="/tags">
                    <Tag className="h-5 w-5" />
                    <span>Tags</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton asChild isActive={isActive("/shared")}>
                  <Link href="/shared">
                    <Link2 className="h-5 w-5" />
                    <span>Shared Links</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroup>
        </SidebarContent>

        <SidebarFooter className="border-t border-sidebar-border px-4 py-2">
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton asChild isActive={isActive("/settings")}>
                <Link href="/settings">
                  <Settings className="h-5 w-5" />
                  <span>Settings</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <SidebarMenuButton onClick={handleLogout}>
                <LogOut className="h-5 w-5" />
                <span>Logout</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
      </Sidebar>

      <AddFolderModal
        open={showAddFolderModal}
        onOpenChange={setShowAddFolderModal}
      />
    </>
  );
}
