"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import {
  Home,
  Library,
  Sparkles,
  Tag,
  Link2,
  Settings,
  LogOut,
  Inbox,
  Folder,
  ChevronRight,
  ChevronDown,
  Plus,
} from "lucide-react";
import { Logo } from "@/components/Logo";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { AddFolderModal } from "@/components/modals/AddFolderModal";

const navigationItems = [
  { title: "Dashboard", href: "/dashboard", icon: Home },
  { title: "Inbox", href: "/inbox", icon: Inbox },
  { title: "All Content", href: "/content", icon: Library },
  { title: "My Gists", href: "/gists", icon: Sparkles },
];

const mockFolders = [
  { id: 1, name: "<PERSON>", count: 12, children: [
    { id: 2, name: "React", count: 8 },
    { id: 3, name: "TypeScript", count: 4 },
  ]},
  { id: 4, name: "Design", count: 6, children: [
    { id: 5, name: "UI/UX", count: 4 },
    { id: 6, name: "Icons", count: 2 },
  ]},
  { id: 7, name: "Marketing", count: 3 },
];

interface CustomSidebarProps {
  open: boolean;
  onToggle: () => void;
}

export function CustomSidebar({ open, onToggle }: CustomSidebarProps) {
  const currentPath = usePathname();
  const [foldersOpen, setFoldersOpen] = useState(true);
  const [expandedFolders, setExpandedFolders] = useState<Set<number>>(
    new Set([1, 4])
  );
  const [showAddFolderModal, setShowAddFolderModal] = useState(false);

  const isActive = (path: string) => currentPath === path;

  const toggleFolder = (folderId: number) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(folderId)) {
      newExpanded.delete(folderId);
    } else {
      newExpanded.add(folderId);
    }
    setExpandedFolders(newExpanded);
  };

  const handleLogout = () => {
    toast.success("Signed out successfully");
  };

  return (
    <>
      <div className={`${open ? "w-64" : "w-16"} transition-all duration-300 bg-sidebar border-r border-border flex flex-col h-full`}>
        {/* Header */}
        <div className="border-b border-border p-4">
          <div className="flex items-center justify-center">
            <Logo 
              variant={open ? "full" : "icon"} 
              size={open ? "md" : "sm"}
              className="transition-all duration-300"
            />
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-2">
          {/* Main Navigation */}
          <div className="space-y-1 mb-4">
            {navigationItems.map((item) => (
              <Link
                key={item.title}
                href={item.href}
                className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 ${
                  isActive(item.href)
                    ? "bg-primary text-primary-foreground shadow-glow" 
                    : "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                }`}
              >
                <item.icon className="h-5 w-5 flex-shrink-0" />
                {open && (
                  <span className="font-medium truncate">
                    {item.title}
                  </span>
                )}
              </Link>
            ))}
          </div>

          {/* Folders Section */}
          <Collapsible open={foldersOpen} onOpenChange={setFoldersOpen}>
            <CollapsibleTrigger asChild>
              <div className="flex items-center justify-between cursor-pointer hover:bg-sidebar-accent rounded px-2 py-1 mb-2">
                <span className="flex items-center gap-2">
                  <Folder className="h-4 w-4" />
                  {open && "Folders"}
                </span>
                <div className="flex items-center gap-1">
                  {open && (
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-5 w-5 p-0 hover:bg-sidebar-accent"
                      onClick={(e) => {
                        e.stopPropagation();
                        setShowAddFolderModal(true);
                      }}
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  )}
                  {open && (
                    foldersOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />
                  )}
                </div>
              </div>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <div className="space-y-1">
                {mockFolders.map((folder) => (
                  <div key={folder.id}>
                    <div className="flex items-center justify-between w-full px-3 py-2 hover:bg-sidebar-accent rounded-lg">
                      <div className="flex items-center space-x-2">
                        {folder.children && (
                          <button 
                            onClick={() => toggleFolder(folder.id)}
                            className="p-0 h-4 w-4 flex items-center justify-center"
                          >
                            {expandedFolders.has(folder.id) ? 
                              <ChevronDown className="h-3 w-3" /> : 
                              <ChevronRight className="h-3 w-3" />
                            }
                          </button>
                        )}
                        <Folder className="h-4 w-4 flex-shrink-0" />
                        {open && <span className="truncate">{folder.name}</span>}
                      </div>
                      {open && (
                        <span className="text-xs text-muted-foreground bg-sidebar-accent px-1.5 py-0.5 rounded">
                          {folder.count}
                        </span>
                      )}
                    </div>
                    
                    {/* Folder Children */}
                    {folder.children && expandedFolders.has(folder.id) && (
                      <div className="ml-4 space-y-1">
                        {folder.children.map((child) => (
                          <div key={child.id} className="flex items-center justify-between w-full px-3 py-2 hover:bg-sidebar-accent rounded-lg">
                            <div className="flex items-center space-x-2">
                              <div className="w-4" />
                              <Folder className="h-3 w-3 flex-shrink-0 opacity-60" />
                              {open && <span className="truncate text-sm">{child.name}</span>}
                            </div>
                            {open && (
                              <span className="text-xs text-muted-foreground bg-sidebar-accent px-1.5 py-0.5 rounded">
                                {child.count}
                              </span>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CollapsibleContent>
          </Collapsible>

          {/* Tags Section */}
          <div className="space-y-1 mt-4">
            <Link
              href="/tags"
              className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 ${
                isActive("/tags")
                  ? "bg-primary text-primary-foreground" 
                  : "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
              }`}
            >
              <Tag className="h-5 w-5 flex-shrink-0" />
              {open && <span className="font-medium">Tags</span>}
            </Link>
            <Link
              href="/shared"
              className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 ${
                isActive("/shared")
                  ? "bg-primary text-primary-foreground" 
                  : "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
              }`}
            >
              <Link2 className="h-5 w-5 flex-shrink-0" />
              {open && <span className="font-medium">Shared Links</span>}
            </Link>
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-border p-4 space-y-1">
          <Link
            href="/settings"
            className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 ${
              isActive("/settings")
                ? "bg-primary text-primary-foreground" 
                : "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
            }`}
          >
            <Settings className="h-5 w-5 flex-shrink-0" />
            {open && <span className="font-medium">Settings</span>}
          </Link>
          <Button
            variant="ghost"
            onClick={handleLogout}
            className="w-full justify-start space-x-3 px-3 py-2 h-auto hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
          >
            <LogOut className="h-5 w-5 flex-shrink-0" />
            {open && <span className="font-medium">Logout</span>}
          </Button>
        </div>
      </div>
      
      <AddFolderModal 
        open={showAddFolderModal} 
        onOpenChange={setShowAddFolderModal}
      />
    </>
  );
}
